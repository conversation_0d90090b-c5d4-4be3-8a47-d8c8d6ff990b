apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: synergychat-crawler
  name: synergychat-crawler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: synergychat-crawler
  template:
    metadata:
      labels:
        app: synergychat-crawler
    spec:
      containers:
        - image: bootdotdev/synergychat-crawler:latest
          name: synergychat-crawler
          envFrom:
            - configMapRef:
                name: synergychat-crawler-configmap
